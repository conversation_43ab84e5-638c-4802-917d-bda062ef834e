/** @odoo-module **/

// 警告标签页样式应用函数
function applyWarningTabStyles() {
    // 查找所有包含"警告消息"的标签页
    const warningTabs = document.querySelectorAll('.o_notebook .nav-tabs .nav-link');

    warningTabs.forEach(tab => {
        if (tab.textContent && tab.textContent.trim().includes('警告消息')) {
            // 添加警告样式类
            tab.classList.add('warning-tab');
        }
    });

    // 也处理可能的动态生成的标签页
    const allTabs = document.querySelectorAll('.nav-tabs .nav-link');
    allTabs.forEach(tab => {
        if (tab.textContent && tab.textContent.trim().includes('警告消息')) {
            tab.classList.add('warning-tab');
        }
    });
}

// 使用MutationObserver监听DOM变化
function initWarningTabObserver() {
    const observer = new MutationObserver((mutations) => {
        let shouldApplyStyles = false;

        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 检查是否添加了新的标签页或笔记本
                        if (node.classList && (
                            node.classList.contains('nav-tabs') ||
                            node.classList.contains('o_notebook') ||
                            node.classList.contains('nav-link') ||
                            node.querySelector && (
                                node.querySelector('.nav-tabs') ||
                                node.querySelector('.nav-link')
                            )
                        )) {
                            shouldApplyStyles = true;
                        }

                        // 检查文本内容是否包含"警告消息"
                        if (node.textContent && node.textContent.includes('警告消息')) {
                            shouldApplyStyles = true;
                        }
                    }
                });
            }

            // 检查文本变化
            if (mutation.type === 'characterData' || mutation.type === 'childList') {
                if (mutation.target.textContent && mutation.target.textContent.includes('警告消息')) {
                    shouldApplyStyles = true;
                }
            }
        });

        if (shouldApplyStyles) {
            // 延迟执行以确保DOM完全更新
            setTimeout(applyWarningTabStyles, 50);
        }
    });

    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });

    return observer;
}

// 初始化
let observer;

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        applyWarningTabStyles();
        observer = initWarningTabObserver();
    });
} else {
    applyWarningTabStyles();
    observer = initWarningTabObserver();
}

// 定期检查（作为备用方案）
setInterval(applyWarningTabStyles, 2000);

// 导出给Odoo使用
if (typeof window !== 'undefined') {
    window.applyWarningTabStyles = applyWarningTabStyles;
}
