/** @odoo-module **/

// 警告标签页样式应用函数
function applyWarningTabStyles() {
    // 查找所有包含"警告消息"的标签页
    const warningTabs = document.querySelectorAll('.o_notebook .nav-tabs .nav-link, .nav-tabs .nav-link');

    warningTabs.forEach(tab => {
        if (tab.textContent && tab.textContent.trim().includes('警告消息')) {
            // 添加警告样式类
            tab.classList.add('warning-tab');
        }
    });
}

// 使用MutationObserver监听DOM变化
function initWarningTabObserver() {
    const observer = new MutationObserver(() => {
        // 延迟执行以确保DOM完全更新
        setTimeout(applyWarningTabStyles, 100);
    });

    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    return observer;
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    applyWarningTabStyles();
    initWarningTabObserver();
});

// 定期检查（作为备用方案）
setInterval(applyWarningTabStyles, 3000);
