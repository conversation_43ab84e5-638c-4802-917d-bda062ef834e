/* 警告标签页样式 - 通用解决方案 */

/* 脉冲动画定义 */
@keyframes warningPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.6;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 警告标签页基础样式 - 使用属性选择器匹配所有包含"警告消息"的标签 */
.o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"],
.o_notebook .nav-tabs .nav-link[href*="warning_note"],
.o_notebook .nav-tabs .nav-link.warning-tab {
    color: #dc3545 !important;
    font-weight: bold !important;
    position: relative;
    overflow: visible !important;
}

/* 小红点样式 - 使用伪元素 */
.o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"]::after,
.o_notebook .nav-tabs .nav-link[href*="warning_note"]::after,
.o_notebook .nav-tabs .nav-link.warning-tab::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    background-color: #dc3545;
    border-radius: 50%;
    animation: warningPulse 2s infinite;
    z-index: 1000;
    box-shadow: 0 0 3px rgba(220, 53, 69, 0.5);
}

/* 悬停效果 */
.o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"]:hover,
.o_notebook .nav-tabs .nav-link[href*="warning_note"]:hover,
.o_notebook .nav-tabs .nav-link.warning-tab:hover {
    color: #a02834 !important;
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-radius: 4px;
    transition: all 0.3s ease;
}

/* 活动状态样式 */
.o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"].active,
.o_notebook .nav-tabs .nav-link[href*="warning_note"].active,
.o_notebook .nav-tabs .nav-link.warning-tab.active {
    background-color: rgba(220, 53, 69, 0.15) !important;
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"]::after,
    .o_notebook .nav-tabs .nav-link[href*="warning_note"]::after,
    .o_notebook .nav-tabs .nav-link.warning-tab::after {
        width: 6px;
        height: 6px;
        top: 1px;
        right: 1px;
    }
}

/* 确保在所有Odoo版本中都能正常工作 */
.o_form_view .o_notebook .nav-tabs .nav-link {
    overflow: visible !important;
}

/* 针对动态生成的标签页 */
.nav-tabs .nav-link[aria-controls*="warning"] {
    color: #dc3545 !important;
    font-weight: bold !important;
    position: relative;
}

.nav-tabs .nav-link[aria-controls*="warning"]::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    background-color: #dc3545;
    border-radius: 50%;
    animation: warningPulse 2s infinite;
    z-index: 1000;
}
