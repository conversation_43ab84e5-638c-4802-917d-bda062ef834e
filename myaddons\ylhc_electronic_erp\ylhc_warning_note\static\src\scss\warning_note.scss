// 警告标签页样式 - SCSS版本

// 变量定义
$warning-color: #dc3545;
$warning-color-dark: #a02834;
$warning-bg-light: rgba(220, 53, 69, 0.1);
$warning-bg-active: rgba(220, 53, 69, 0.15);
$dot-size: 8px;
$dot-size-mobile: 6px;

// 脉冲动画
@keyframes warningPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.6;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

// 警告标签页基础样式
.o_notebook .nav-tabs {
    .nav-link {
        // 匹配包含warning_note的标签页
        &[data-bs-target*="warning_note"],
        &[href*="warning_note"],
        &.warning-tab {
            color: $warning-color !important;
            font-weight: bold !important;
            position: relative;
            overflow: visible !important;

            // 小红点样式
            &::after {
                content: '';
                position: absolute;
                top: 2px;
                right: 2px;
                width: $dot-size;
                height: $dot-size;
                background-color: $warning-color;
                border-radius: 50%;
                animation: warningPulse 2s infinite;
                z-index: 1000;
                box-shadow: 0 0 3px rgba(220, 53, 69, 0.5);
            }

            // 悬停效果
            &:hover {
                color: $warning-color-dark !important;
                background-color: $warning-bg-light !important;
                border-radius: 4px;
                transition: all 0.3s ease;
            }

            // 活动状态
            &.active {
                background-color: $warning-bg-active !important;
                border-color: $warning-color !important;
                color: $warning-color !important;
            }
        }
    }
}

// 针对不同的Odoo版本兼容性
.o_form_view .o_notebook .nav-tabs .nav-link {
    overflow: visible !important;
}

// 针对动态生成的标签页
.nav-tabs .nav-link {
    &[aria-controls*="warning"] {
        color: $warning-color !important;
        font-weight: bold !important;
        position: relative;

        &::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: $dot-size;
            height: $dot-size;
            background-color: $warning-color;
            border-radius: 50%;
            animation: warningPulse 2s infinite;
            z-index: 1000;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .o_notebook .nav-tabs .nav-link {
        &[data-bs-target*="warning_note"],
        &[href*="warning_note"],
        &.warning-tab {
            &::after {
                width: $dot-size-mobile;
                height: $dot-size-mobile;
                top: 1px;
                right: 1px;
            }
        }
    }

    .nav-tabs .nav-link[aria-controls*="warning"]::after {
        width: $dot-size-mobile;
        height: $dot-size-mobile;
        top: 1px;
        right: 1px;
    }
}

// 确保小红点在所有情况下都可见
.o_notebook .nav-tabs .nav-link,
.nav-tabs .nav-link {
    overflow: visible !important;
}

// 特殊情况：如果标签页有图标
.o_notebook .nav-tabs .nav-link {
    &[data-bs-target*="warning_note"],
    &[href*="warning_note"],
    &.warning-tab {
        .fa, .oi {
            color: $warning-color !important;
        }
    }
}
