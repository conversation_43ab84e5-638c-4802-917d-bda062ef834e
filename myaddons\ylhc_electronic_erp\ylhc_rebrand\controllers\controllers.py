# -*- coding: utf-8 -*-
# from odoo import http


# class Ylhc<PERSON><PERSON><PERSON>(http.Controller):
#     @http.route('/ylhc_rebrand/ylhc_rebrand', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/ylhc_rebrand/ylhc_rebrand/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('ylhc_rebrand.listing', {
#             'root': '/ylhc_rebrand/ylhc_rebrand',
#             'objects': http.request.env['ylhc_rebrand.ylhc_rebrand'].search([]),
#         })

#     @http.route('/ylhc_rebrand/ylhc_rebrand/objects/<model("ylhc_rebrand.ylhc_rebrand"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('ylhc_rebrand.object', {
#             'object': obj
#         })

