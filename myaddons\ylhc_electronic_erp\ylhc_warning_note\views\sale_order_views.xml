<odoo>
    <data>
        <!-- 添加CSS和JS资源 -->
        <template id="warning_note_assets" name="Warning Note Assets" inherit_id="web.assets_backend">
            <xpath expr="." position="inside">
                <style>
                    /* 警告标签页样式 */
                    @keyframes warningPulse {
                        0% { transform: scale(1); opacity: 1; }
                        50% { transform: scale(1.3); opacity: 0.6; }
                        100% { transform: scale(1); opacity: 1; }
                    }

                    /* 基础样式 - 匹配所有包含warning_note的标签页 */
                    .o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"],
                    .o_notebook .nav-tabs .nav-link[href*="warning_note"],
                    .o_notebook .nav-tabs .nav-link.warning-tab {
                        color: #dc3545 !important;
                        font-weight: bold !important;
                        position: relative;
                        overflow: visible !important;
                    }

                    /* 小红点 */
                    .o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"]::after,
                    .o_notebook .nav-tabs .nav-link[href*="warning_note"]::after,
                    .o_notebook .nav-tabs .nav-link.warning-tab::after {
                        content: '';
                        position: absolute;
                        top: 2px;
                        right: 2px;
                        width: 8px;
                        height: 8px;
                        background-color: #dc3545;
                        border-radius: 50%;
                        animation: warningPulse 2s infinite;
                        z-index: 1000;
                        box-shadow: 0 0 3px rgba(220, 53, 69, 0.5);
                    }

                    /* 悬停效果 */
                    .o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"]:hover,
                    .o_notebook .nav-tabs .nav-link[href*="warning_note"]:hover,
                    .o_notebook .nav-tabs .nav-link.warning-tab:hover {
                        color: #a02834 !important;
                        background-color: rgba(220, 53, 69, 0.1) !important;
                        border-radius: 4px;
                        transition: all 0.3s ease;
                    }

                    /* 活动状态 */
                    .o_notebook .nav-tabs .nav-link[data-bs-target*="warning_note"].active,
                    .o_notebook .nav-tabs .nav-link[href*="warning_note"].active,
                    .o_notebook .nav-tabs .nav-link.warning-tab.active {
                        background-color: rgba(220, 53, 69, 0.15) !important;
                        border-color: #dc3545 !important;
                        color: #dc3545 !important;
                    }
                </style>
                <script>
                    // 简单的JavaScript来添加warning-tab类
                    document.addEventListener('DOMContentLoaded', function() {
                        function applyWarningStyles() {
                            const tabs = document.querySelectorAll('.o_notebook .nav-tabs .nav-link');
                            tabs.forEach(tab => {
                                if (tab.textContent && tab.textContent.includes('警告消息')) {
                                    tab.classList.add('warning-tab');
                                }
                            });
                        }

                        applyWarningStyles();

                        // 监听DOM变化
                        const observer = new MutationObserver(function() {
                            setTimeout(applyWarningStyles, 100);
                        });

                        observer.observe(document.body, {
                            childList: true,
                            subtree: true
                        });
                    });
                </script>
            </xpath>
        </template>

        <record model="ir.ui.view" id="view_order_form_inherit">
            <field name="name">sale.order.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='other_information']" position="after">
                    <page string="警告消息" name="warning_note" invisible="not warn_note_page" class="warning-note-page">
                        <field name="warn_note" readonly="1" widget="text" style="color: #b94a48; background: #f2dede; border: 1px solid #eed3d7; padding: 8px; min-height: 60px; font-size: 20px; font-weight: bold;"/>
                    </page>
                </xpath>
            </field>
        </record>
    </data>
</odoo>